{"format": 1, "restore": {"/home/<USER>/Code/ShingMoney/Api/Api.csproj": {}}, "projects": {"/home/<USER>/Code/ShingMoney/Api/Api.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "/home/<USER>/Code/ShingMoney/Api/Api.csproj", "projectName": "Api", "projectPath": "/home/<USER>/Code/ShingMoney/Api/Api.csproj", "packagesPath": "/home/<USER>/.nuget/packages/", "outputPath": "/home/<USER>/Code/ShingMoney/Api/obj/", "projectStyle": "PackageReference", "configFilePaths": ["/home/<USER>/.nuget/NuGet/NuGet.Config"], "originalTargetFrameworks": ["net9.0"], "sources": {"https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Swashbuckle.AspNetCore": {"target": "Package", "version": "[9.0.3, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "/usr/share/dotnet/sdk/9.0.303/PortableRuntimeIdentifierGraph.json"}}}}}