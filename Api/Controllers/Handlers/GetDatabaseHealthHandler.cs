using Microsoft.AspNetCore.Mvc;

namespace Api.Controllers.Handlers;

public class GetDatabaseHealthHandler
{
    public ActionResult<GetDatabaseHealthResponse> GetDatabaseHealthResponse Execute(GetDatabaseHealthRequest request)
    {
        return Ok(new GetDatabaseHealthResponse { IsHealthy = true });
    }
}

public class GetDatabaseHealthRequest
{
}

public class GetDatabaseHealthResponse
{
    public required bool IsHealthy { get; set; }
}