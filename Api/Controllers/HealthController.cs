using Microsoft.AspNetCore.Mvc;

namespace Api.Controllers
{
	[ApiController]
	[Route("[controller]")]
	public class WeatherForecastController : ControllerBase
	{
		private readonly ILogger<WeatherForecastController> _logger;

		public WeatherForecastController(ILogger<WeatherForecastController> logger)
		{
			_logger = logger;
		}

		[HttpGet(Name = "database")]
		public ActionResult<GetDatabaseHealthResponse> GetDatabaseHealth()
		{
			return new OkResult
			return new GetDatabaseHealthHandler()
		}
	}
}
